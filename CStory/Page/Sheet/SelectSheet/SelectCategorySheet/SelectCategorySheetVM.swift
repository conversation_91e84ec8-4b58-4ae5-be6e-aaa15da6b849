//
//  SelectCategorySheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/6.
//

import Foundation
import SwiftUI

/// 交易分类选择弹窗 ViewModel
///
/// 管理交易分类选择界面的状态和业务逻辑，包括：
/// - 分类列表筛选和展示
/// - 主分类/子分类选择逻辑
/// - 展开/收起子分类管理
/// - 选择确认处理
class SelectCategorySheetVM: ObservableObject {

  // MARK: - 属性

  /// 交易类型
  let transactionType: TransactionType
  /// 选中的分类ID
  @Binding var selectedCategoryId: String?
  /// 主分类列表
  private let categories: [TransactionMainCategoryModel]
  /// 子分类列表
  private let subCategories: [TransactionSubCategoryModel]
  /// 数据管理器
  private let dataManager: DataManagement

  // MARK: - 状态

  /// 选中的主分类
  @Published var selectedMainCategory: TransactionMainCategoryModel?
  /// 选中的子分类
  @Published var selectedSubCategory: TransactionSubCategoryModel?
  /// 是否显示子分类
  @Published var showSubCategories: Bool = false

  // MARK: - 回调

  /// 分类选择回调
  let onCategorySelected: (String) -> Void
  /// 取消回调
  let onCancel: () -> Void

  // MARK: - 初始化

  init(
    transactionType: TransactionType,
    selectedCategoryId: Binding<String?>,
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel],
    dataManager: DataManagement,
    onCategorySelected: @escaping (String) -> Void,
    onCancel: @escaping () -> Void
  ) {
    self.transactionType = transactionType
    self._selectedCategoryId = selectedCategoryId
    self.categories = categories
    self.subCategories = subCategories
    self.dataManager = dataManager
    self.onCategorySelected = onCategorySelected
    self.onCancel = onCancel

    // 初始化选中状态
    setupInitialSelection()
  }

  // MARK: - 计算属性

  /// 获取过滤后的主分类列表
  var filteredCategories: [TransactionMainCategoryModel] {
    return categories.filter { $0.type == transactionType.rawValue }
      .sorted { $0.order < $1.order }
  }

  /// 获取选中主分类的子分类列表
  var selectedMainCategorySubCategories: [TransactionSubCategoryModel] {
    guard let mainCategory = selectedMainCategory else { return [] }
    return subCategories.filter { $0.mainId == mainCategory.id }
      .sorted { $0.order < $1.order }
  }

  /// 是否有选中的分类
  var hasSelectedCategory: Bool {
    return selectedSubCategory != nil || selectedMainCategory != nil
  }

  /// 获取选中的分类ID
  var finalSelectedCategoryId: String? {
    return selectedSubCategory?.id ?? selectedMainCategory?.id
  }

  // MARK: - 私有方法

  /// 设置初始选择状态
  private func setupInitialSelection() {
    guard let categoryId = selectedCategoryId else { return }

    // 检查是否是子分类
    if let subCategory = subCategories.first(where: { $0.id == categoryId }) {
      selectedSubCategory = subCategory
      // 找到对应的主分类并展开
      if let mainCategory = categories.first(where: { $0.id == subCategory.mainId }) {
        selectedMainCategory = mainCategory
        showSubCategories = true
      }
    }
    // 检查是否是主分类
    else if let mainCategory = categories.first(where: { $0.id == categoryId }) {
      selectedMainCategory = mainCategory
    }
  }

  // MARK: - 公共方法

  /// 处理取消操作
  @MainActor
  func handleCancel() {
    print("🔍 [SelectCategorySheetVM] handleCancel 被调用")
    dataManager.hapticManager.trigger(.impactLight)
    onCancel()
  }

  /// 处理主分类点击
  /// - Parameter mainCategory: 被点击的主分类
  @MainActor
  func handleMainCategoryTap(_ mainCategory: TransactionMainCategoryModel) {
    dataManager.hapticManager.trigger(.selection)

    let hasSubCategories = !subCategories.filter { $0.mainId == mainCategory.id }.isEmpty

    if hasSubCategories {
      // 有子分类，展开/收起子分类
      if selectedMainCategory?.id == mainCategory.id {
        withAnimation(.easeInOut(duration: 0.3)) {
          showSubCategories.toggle()
        }
      } else {
        selectedMainCategory = mainCategory
        selectedSubCategory = nil
        withAnimation(.easeInOut(duration: 0.3)) {
          showSubCategories = true
        }
      }
    } else {
      // 没有子分类，直接选中主分类
      selectedMainCategory = mainCategory
      selectedSubCategory = nil
      showSubCategories = false
    }
  }

  /// 处理子分类点击
  /// - Parameter subCategory: 被点击的子分类
  @MainActor
  func handleSubCategoryTap(_ subCategory: TransactionSubCategoryModel) {
    dataManager.hapticManager.trigger(.selection)
    selectedSubCategory = subCategory
  }

  /// 处理确认选择
  @MainActor
  func handleConfirmSelection() {
    print(
      "🔍 [SelectCategorySheetVM] handleConfirmSelection 被调用，categoryId: \(finalSelectedCategoryId ?? "nil")"
    )
    dataManager.hapticManager.trigger(.impactMedium)
    if let categoryId = finalSelectedCategoryId {
      onCategorySelected(categoryId)
    }
  }

  /// 检查主分类是否被选中
  /// - Parameter mainCategory: 要检查的主分类
  /// - Returns: 是否被选中
  func isMainCategorySelected(_ mainCategory: TransactionMainCategoryModel) -> Bool {
    if let selectedSub = selectedSubCategory {
      // 如果有选中的子分类，检查是否属于这个主分类
      return selectedSub.mainId == mainCategory.id
    }
    return selectedMainCategory?.id == mainCategory.id
  }

  /// 检查子分类是否被选中
  /// - Parameter subCategory: 要检查的子分类
  /// - Returns: 是否被选中
  func isSubCategorySelected(_ subCategory: TransactionSubCategoryModel) -> Bool {
    return selectedSubCategory?.id == subCategory.id
  }

  /// 检查主分类是否应该显示子分类
  /// - Parameter mainCategory: 要检查的主分类
  /// - Returns: 是否应该显示子分类
  func shouldShowSubCategories(for mainCategory: TransactionMainCategoryModel) -> Bool {
    return selectedMainCategory?.id == mainCategory.id && showSubCategories
  }

  /// 获取主分类的子分类列表
  /// - Parameter mainCategory: 主分类
  /// - Returns: 子分类列表
  func getSubCategories(for mainCategory: TransactionMainCategoryModel)
    -> [TransactionSubCategoryModel]
  {
    return subCategories.filter { $0.mainId == mainCategory.id }
      .sorted { $0.order < $1.order }
  }

  /// 检查主分类是否有子分类
  /// - Parameter mainCategory: 要检查的主分类
  /// - Returns: 是否有子分类
  func hasSubCategories(_ mainCategory: TransactionMainCategoryModel) -> Bool {
    return !subCategories.filter { $0.mainId == mainCategory.id }.isEmpty
  }
}
